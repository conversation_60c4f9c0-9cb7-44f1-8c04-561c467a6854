<template>
  <section class="cert-template-container">

    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-input
        v-model="searchName"
        placeholder="请输入模板名称搜索"
        prefix-icon="el-icon-search"
        style="width: 300px; margin-bottom: 20px;"
        @input="handleSearch"
        clearable
      />
    </div>

    <!-- 添加模板按钮 -->
    <div class="add-template-btn-top">
      <el-button type="primary" size="medium" icon="el-icon-plus" @click="addTemplate">
        添加新模板
      </el-button>
    </div>

    <!-- 模板卡片列表 -->
    <div class="template-cards-container">
      <div v-if="certTemplList.length === 0" class="empty-state">
        <el-empty description="暂无模板数据" />
      </div>

      <div v-else class="cards-grid">
        <div
          v-for="template in certTemplList"
          :key="template.id"
          class="template-card"
        >
          <!-- 主模板卡片 -->
          <el-card class="parent-card" shadow="hover">
            <div class="card-header">
              <div class="template-info">
                <h3 class="template-name">{{ template.name }}</h3>
                <div class="template-meta">
                  <span class="meta-time">{{ formatTime(template.createTime) }}</span>
                  <span v-if="template.regularExpressions" class="meta-regex" :title="template.regularExpressions">
                    正则
                  </span>
                  <span class="meta-accounts" :title="getTemplateAccountInfo(template)">
                    {{ getTemplateAccountInfo(template) }}
                  </span>
                </div>
              </div>
              <div class="card-actions">
                <el-button type="success" size="small" icon="el-icon-setting" @click="manageTemplateAccounts(template)">
                  帐套管理
                </el-button>
                <el-button type="primary" size="small" icon="el-icon-edit" @click="editTemplate(template)">
                  编辑
                </el-button>
              </div>
            </div>

            <!-- 子项展开/收起 -->
            <div v-if="template.hasChildren && template.children && template.children.length > 0" class="children-section">
              <div class="children-header">
                <span class="children-title">
                  会计科目 ({{ template.children.length }})
                </span>
              </div>

              <!-- 子项列表 - 默认显示前3条 -->
              <div class="children-list">
                <div
                  v-for="(child, index) in getVisibleChildren(template)"
                  :key="child.id"
                  class="child-item"
                >
                  <div class="child-content">
                    <div class="child-main">
                      <h4 class="child-name">{{ child.name }}</h4>
                      <p class="child-summary">{{ child.fieldSummary }}</p>
                    </div>
                    <div class="child-details">
                      <div class="detail-row">
                        <span class="detail-label">借贷:</span>
                        <span class="detail-value">{{ child.borrowing == 1 ? '借' : '贷' }}</span>
                      </div>
                      <div class="detail-row">
                        <span class="detail-label">摘要:</span>
                        <span class="detail-value">{{ child.templAbstract }}</span>
                      </div>
                      <!-- 显示关联的帐套信息 -->
                      <div class="detail-row account-sets-row">
                        <span class="detail-label">关联帐套:</span>
                        <div class="account-sets-info">
                          <div v-if="getChildAccountSets(template.id, child.id).length === 0" class="no-accounts">
                            <span class="no-accounts-text">未关联帐套</span>
                          </div>
                          <div v-else class="account-sets-list">
                            <div
                              v-for="accountSet in getChildAccountSets(template.id, child.id)"
                              :key="`${accountSet.companyId}-${accountSet.accModel}`"
                              class="account-set-item"
                            >
                              <el-tag
                                :type="accountSet.accModel === 1 ? 'success' : 'warning'"
                                size="mini"
                                class="account-tag"
                              >
                                {{ accountSet.companyName }} - {{ accountSet.accModel === 1 ? '税务' : '股东' }}
                              </el-tag>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="child-actions">
                      <el-button type="text" size="mini" @click="manageChildAccountSets(child, template)">
                        帐套绑定
                      </el-button>
                      <el-button type="text" size="mini" @click="editChild(child, template)">
                        编辑
                      </el-button>
                    </div>
                  </div>
                </div>

                <!-- 展开/收起按钮 -->
                <div v-if="template.children.length > 3" class="expand-toggle-btn">
                  <el-button
                    type="text"
                    size="small"
                    @click="toggleChildren(template)"
                    class="toggle-btn"
                  >
                    <i :class="template.expanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
                    {{ template.expanded ? '收起' : `展开更多 (${template.children.length - 3})` }}
                  </el-button>
                </div>


              </div>
            </div>
              <!-- 添加子项按钮 -->
            <div class="add-child-btn">
              <el-button type="dashed" size="small" icon="el-icon-plus" @click="addChild(template)">
                添加会计科目
              </el-button>
            </div>
          </el-card>
        </div>
      </div>
    </div>

    <!-- 模板编辑对话框 -->
    <el-dialog
      :title="dialogMode === 'add' ? '添加模板' : '编辑模板'"
      :visible.sync="templateDialogVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="templateForm"
        :model="templateForm"
        :rules="templateRules"
        label-width="120px"
      >
        <el-form-item label="模板名称" prop="name">
          <el-input v-model="templateForm.name" placeholder="请输入模板名称" />
        </el-form-item>
        <el-form-item label="模板编码" prop="code">
          <el-input v-model="templateForm.code" placeholder="请输入模板编码" />
        </el-form-item>
        <el-form-item label="应用编码" prop="appCode">
          <el-input v-model="templateForm.appCode" placeholder="请输入应用编码" />
        </el-form-item>
        <el-form-item label="正则表达式">
          <el-input
            v-model="templateForm.regularExpressions"
            type="textarea"
            :rows="3"
            placeholder="请输入正则表达式（可选）"
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="templateDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveTemplate" :loading="saving">
          {{ dialogMode === 'add' ? '添加' : '保存' }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 会计科目编辑对话框 -->
    <el-dialog
      :title="childDialogMode === 'add' ? '添加会计科目' : '编辑会计科目'"
      :visible.sync="childDialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="childForm"
        :model="childForm"
        :rules="childRules"
        label-width="120px"
      >
        <el-form-item label="科目名称" prop="name">
          <el-input v-model="childForm.name" placeholder="请输入科目名称" />
        </el-form-item>
        <el-form-item label="字段编码" prop="fieldCode">
          <el-input v-model="childForm.fieldCode" placeholder="请输入字段编码" />
        </el-form-item>
        <el-form-item label="科目" prop="fieldSummary">
          <el-input v-model="childForm.fieldSummary" placeholder="请输入科目" />
        </el-form-item>
        <el-form-item label="会计科目" prop="accountingSubjects">
          <el-input v-model="childForm.accountingSubjects" placeholder="请输入会计科目代码" />
        </el-form-item>
        <el-form-item label="借贷方向" prop="borrowing">
          <el-select v-model="childForm.borrowing" placeholder="请选择借贷方向">
            <el-option label="借" :value="1" />
            <el-option label="贷" :value="-1" />
          </el-select>
        </el-form-item>
        <el-form-item label="模板摘要" prop="templAbstract">
          <el-input v-model="childForm.templAbstract" placeholder="请输入模板摘要" />
        </el-form-item>
        <el-form-item label="排序号" prop="ordernum">
          <el-input-number v-model="childForm.ordernum" :min="1" placeholder="排序号" />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="childDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveChild" :loading="saving">
          {{ childDialogMode === 'add' ? '添加' : '保存' }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 帐套管理对话框 -->
    <el-dialog
      :title="`${currentManageTemplate ? currentManageTemplate.name : ''} - 帐套管理`"
      :visible.sync="accountManageDialogVisible"
      width="1200px"
      :close-on-click-modal="false"
    >
      <div class="account-manage-content">
        <div class="manage-header">
          <div class="template-info">
            <span class="info-label">模板编码：</span>
            <span class="info-value">{{ currentManageTemplate ? currentManageTemplate.code : '' }}</span>
            <span class="info-label" style="margin-left: 20px;">应用编码：</span>
            <span class="info-value">{{ currentManageTemplate ? currentManageTemplate.appCode : '' }}</span>
          </div>
          <el-button type="primary" size="medium" icon="el-icon-plus" @click="showBatchSelectDialog">
            批量选择帐套
          </el-button>
        </div>

        <div class="account-categories">
          <!-- 税务核算分类 -->
          <div class="category-section">
            <div class="category-header">
              <h3 class="category-title">
                <i class="el-icon-document"></i>
                税务核算
              </h3>
              <span class="category-count">{{ getTaxAccountsCount() }} 个帐套</span>
            </div>

            <div class="accounts-grid">
              <div
                v-for="account in getTaxAccounts()"
                :key="`tax-${account.id}`"
                class="account-card"
              >
                <div class="account-card-header">
                  <h4 class="company-name">{{ account.companyName }}</h4>
                  <el-button type="text" size="mini" style="color: #f56c6c;" @click="removeTemplateAccount(currentManageTemplate.id, account.companyId, 1)">
                    移除
                  </el-button>
                </div>
                <div class="account-info">
                  <div class="info-row">
                    <span class="label">帐套名称:</span>
                    <span class="value">{{ account.appName }}</span>
                  </div>
                  <div class="info-row">
                    <span class="label">帐套编码:</span>
                    <span class="value">{{ account.appCode }}</span>
                  </div>
                </div>
              </div>

              <div v-if="getTaxAccounts().length === 0" class="empty-category">
                <el-empty description="暂无税务核算帐套" />
              </div>
            </div>
          </div>

          <!-- 股东核算分类 -->
          <div class="category-section">
            <div class="category-header">
              <h3 class="category-title">
                <i class="el-icon-user"></i>
                股东核算
              </h3>
              <span class="category-count">{{ getShareholderAccountsCount() }} 个帐套</span>
            </div>

            <div class="accounts-grid">
              <div
                v-for="account in getShareholderAccounts()"
                :key="`shareholder-${account.id}`"
                class="account-card"
              >
                <div class="account-card-header">
                  <h4 class="company-name">{{ account.companyName }}</h4>
                  <el-button type="text" size="mini" style="color: #f56c6c;" @click="removeTemplateAccount(currentManageTemplate.id, account.companyId, 2)">
                    移除
                  </el-button>
                </div>
                <div class="account-info">
                  <div class="info-row">
                    <span class="label">帐套名称:</span>
                    <span class="value">{{ account.appName }}</span>
                  </div>
                  <div class="info-row">
                    <span class="label">帐套编码:</span>
                    <span class="value">{{ account.appCode }}</span>
                  </div>
                </div>
              </div>

              <div v-if="getShareholderAccounts().length === 0" class="empty-category">
                <el-empty description="暂无股东核算帐套" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="accountManageDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 批量选择帐套对话框 -->
    <el-dialog
      title="批量选择帐套"
      :visible.sync="batchSelectDialogVisible"
      width="1000px"
      :close-on-click-modal="false"
    >
      <div class="batch-select-content">
        <div class="select-header">
          <div class="template-info">
            <span class="info-label">当前模板：</span>
            <span class="info-value">{{ currentManageTemplate ? currentManageTemplate.name : '' }}</span>
          </div>
          <div class="batch-actions">
            <el-button size="small" @click="selectAllAccounts">全选</el-button>
            <el-button size="small" @click="clearAllAccounts">清空</el-button>
          </div>
        </div>

        <div class="accounts-selection">
          <div class="selection-section">
            <h4>税务核算帐套</h4>
            <div class="accounts-grid">
              <el-checkbox
                v-for="account in taxAccounts"
                :key="`tax-${account.id}`"
                v-model="account.selected"
                class="account-checkbox"
              >
                <div class="account-info">
                  <div class="account-name">{{ account.companyName }}</div>
                  <div class="account-detail">{{ account.appName }} ({{ account.appCode }})</div>
                </div>
              </el-checkbox>
            </div>
          </div>

          <div class="selection-section">
            <h4>股东核算帐套</h4>
            <div class="accounts-grid">
              <el-checkbox
                v-for="account in shareholderAccounts"
                :key="`shareholder-${account.id}`"
                v-model="account.selected"
                class="account-checkbox"
              >
                <div class="account-info">
                  <div class="account-name">{{ account.companyName }}</div>
                  <div class="account-detail">{{ account.appName }} ({{ account.appCode }})</div>
                </div>
              </el-checkbox>
            </div>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="batchSelectDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveBatchSelection" :loading="saving">
          保存选择
        </el-button>
      </div>
    </el-dialog>

    <!-- 会计科目帐套绑定对话框 -->
    <el-dialog
      :title="`${currentChildSubject ? currentChildSubject.name : ''} - 帐套绑定管理`"
      :visible.sync="childAccountBindingDialogVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="child-account-binding-content">
        <div class="binding-header">
          <div class="subject-info">
            <span class="info-label">科目名称：</span>
            <span class="info-value">{{ currentChildSubject ? currentChildSubject.name : '' }}</span>
            <span class="info-label" style="margin-left: 20px;">科目编码：</span>
            <span class="info-value">{{ currentChildSubject ? currentChildSubject.fieldCode : '' }}</span>
          </div>
        </div>

        <div class="binding-categories">
          <!-- 税务核算帐套 -->
          <div class="binding-section">
            <div class="binding-section-header">
              <h3 class="section-title">
                <i class="el-icon-document"></i>
                税务核算帐套
              </h3>
            </div>
            <div class="account-binding-list">
              <div
                v-for="account in getAvailableTaxAccounts()"
                :key="`tax-${account.companyId}`"
                class="account-binding-item"
              >
                <div class="account-info">
                  <div class="company-name">{{ account.companyName }}</div>
                  <div class="account-detail">{{ account.appName }} ({{ account.appCode }})</div>
                </div>
                <div class="binding-action">
                  <el-switch
                    v-model="account.bound"
                    @change="toggleChildAccountBinding(account, 1)"
                    active-text="已绑定"
                    inactive-text="未绑定"
                  />
                </div>
              </div>
              <div v-if="getAvailableTaxAccounts().length === 0" class="empty-accounts">
                <span>该模板暂无税务核算帐套</span>
              </div>
            </div>
          </div>

          <!-- 股东核算帐套 -->
          <div class="binding-section">
            <div class="binding-section-header">
              <h3 class="section-title">
                <i class="el-icon-user"></i>
                股东核算帐套
              </h3>
            </div>
            <div class="account-binding-list">
              <div
                v-for="account in getAvailableShareholderAccounts()"
                :key="`shareholder-${account.companyId}`"
                class="account-binding-item"
              >
                <div class="account-info">
                  <div class="company-name">{{ account.companyName }}</div>
                  <div class="account-detail">{{ account.appName }} ({{ account.appCode }})</div>
                </div>
                <div class="binding-action">
                  <el-switch
                    v-model="account.bound"
                    @change="toggleChildAccountBinding(account, 2)"
                    active-text="已绑定"
                    inactive-text="未绑定"
                  />
                </div>
              </div>
              <div v-if="getAvailableShareholderAccounts().length === 0" class="empty-accounts">
                <span>该模板暂无股东核算帐套</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="childAccountBindingDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>

  </section>
</template>
<script>
import { getCertTemplList, getCertTemplTempList,getTokenCompanyList,removeCertTemplTemp, saveCertTemplTemp,saveCertTemplVo, updateCertTemplVo} from '@/api/system/baseInit'

export default {
  data() {
    return {
      certTemplList: [],
      searchName: '',
      originalList: [],

      // 凭证科目管理相关数据
      tokenCompanyList: [],
      certTemplTempList: [],
      accountManageDialogVisible: false,
      currentManageTemplate: null,
      batchSelectDialogVisible: false,
      taxAccounts: [],
      shareholderAccounts: [],

      // 会计科目帐套绑定对话框
      childAccountBindingDialogVisible: false,
      currentChildSubject: null,
      currentChildTemplate: null,



      // 模板对话框相关
      templateDialogVisible: false,
      dialogMode: 'add', // 'add' | 'edit'
      templateForm: {
        id: null,
        name: '',
        code: '',
        appCode: '',
        regularExpressions: ''
      },
      templateRules: {
        name: [
          { required: true, message: '请输入模板名称', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入模板编码', trigger: 'blur' },
          { validator: this.validateCode, trigger: 'blur' }
        ],
        appCode: [
          { required: true, message: '请输入应用编码', trigger: 'blur' }
        ]
      },

      // 会计科目对话框相关
      childDialogVisible: false,
      childDialogMode: 'add', // 'add' | 'edit'
      currentTemplate: null, // 当前操作的模板
      childForm: {
        id: null,
        name: '',
        fieldCode: '',
        fieldSummary: '',
        accountingSubjects: '',
        borrowing: 1,
        templAbstract: '',
        ordernum: 1,
        certTemplId: null
      },
      childRules: {
        name: [
          { required: true, message: '请输入科目名称', trigger: 'blur' }
        ],
        fieldCode: [
          { required: true, message: '请输入字段编码', trigger: 'blur' }
        ],
        fieldSummary: [
          { required: true, message: '请输入科目', trigger: 'blur' }
        ],
        accountingSubjects: [
          { required: true, message: '请输入会计科目代码', trigger: 'blur' }
        ],
        borrowing: [
          { required: true, message: '请选择借贷方向', trigger: 'change' }
        ],
        templAbstract: [
          { required: true, message: '请输入模板摘要', trigger: 'blur' }
        ],
        ordernum: [
          { required: true, message: '请输入排序号', trigger: 'blur' }
        ]
      },

      saving: false
    }
  },
  computed: {

  },
  async created() {
    await this.loadCertTemplList()
    await this.loadTokenCompanyList()
    await this.loadCertTemplTempList()
  },
  methods: {
    loadCertTemplList() {
      return getCertTemplList(this.searchName).then(res => {
        // 确保每个模板都有 expanded 属性
        const list = (res.list || res || []).map(template => ({
          ...template,
          expanded: false
        }))
        this.certTemplList = list
        this.originalList = [...list]
      })
    },

    handleSearch() {
      this.loadCertTemplList()
    },

    getVisibleChildren(template) {
      if (!template.children) return []
      if (template.expanded) {
        return template.children
      }
      return template.children.slice(0, 3)
    },

    toggleChildren(template) {
      this.$set(template, 'expanded', !template.expanded)
    },

    formatTime(timeStr) {
      if (!timeStr) return ''
      // 将完整时间格式化为简短格式
      const date = new Date(timeStr)
      const now = new Date()
      const diffTime = now - date
      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

      if (diffDays === 0) {
        return '今天'
      } else if (diffDays === 1) {
        return '昨天'
      } else if (diffDays < 7) {
        return `${diffDays}天前`
      } else if (diffDays < 30) {
        const weeks = Math.floor(diffDays / 7)
        return `${weeks}周前`
      } else if (diffDays < 365) {
        const months = Math.floor(diffDays / 30)
        return `${months}月前`
      } else {
        return timeStr.split(' ')[0] // 返回日期部分
      }
    },

    // 模板相关方法
    addTemplate() {
      this.dialogMode = 'add'
      this.resetTemplateForm()
      this.templateDialogVisible = true
    },

    editTemplate(template) {
      this.dialogMode = 'edit'
      this.templateForm = {
        id: template.id,
        name: template.name,
        code: template.code,
        appCode: template.appCode,
        regularExpressions: template.regularExpressions || ''
      }
      this.templateDialogVisible = true
    },

    resetTemplateForm() {
      this.templateForm = {
        id: null,
        name: '',
        code: '',
        appCode: 'prod1', // 设置默认值为 prod1
        regularExpressions: ''
      }
      if (this.$refs.templateForm) {
        this.$refs.templateForm.clearValidate()
      }
    },

    // 验证编码是否重复
    validateCode(rule, value, callback) {
      if (!value) {
        callback()
        return
      }

      // 如果是编辑模式且编码没有改变，则不需要验证
      if (this.dialogMode === 'edit' && this.templateForm.id) {
        const currentTemplate = this.certTemplList.find(t => t.id === this.templateForm.id)
        if (currentTemplate && currentTemplate.code === value) {
          callback()
          return
        }
      }

      // 检查编码是否已存在
      const existingTemplate = this.certTemplList.find(template =>
        template.code === value && template.id !== this.templateForm.id
      )

      if (existingTemplate) {
        callback(new Error('该编码已存在，请使用其他编码'))
      } else {
        callback()
      }
    },

    saveTemplate() {
      this.$refs.templateForm.validate((valid) => {
        if (valid) {
          this.saving = true
          // 去除parent包装，直接使用扁平化数据结构
          const templateData = {
            ...this.templateForm,
            children: []
          }

          const apiCall = this.dialogMode === 'add'
            ? saveCertTemplVo(templateData)
            : updateCertTemplVo(templateData)

          apiCall.then(res => {
            this.$message.success(this.dialogMode === 'add' ? '添加成功' : '保存成功')
            this.templateDialogVisible = false
            this.loadCertTemplList()
          }).catch(err => {
            this.$message.error(err.message || '操作失败')
          }).finally(() => {
            this.saving = false
          })
        }
      })
    },

    // 会计科目相关方法
    addChild(template) {
      this.childDialogMode = 'add'
      this.currentTemplate = template
      this.resetChildForm()
      this.childForm.certTemplId = template.id
      // 设置默认排序号为当前子项数量+1
      this.childForm.ordernum = (template.children ? template.children.length : 0) + 1
      this.childDialogVisible = true
    },

    editChild(child, template) {
      this.childDialogMode = 'edit'
      this.currentTemplate = template
      this.childForm = {
        id: child.id,
        name: child.name,
        fieldCode: child.fieldCode,
        fieldSummary: child.fieldSummary,
        accountingSubjects: child.accountingSubjects,
        borrowing: child.borrowing,
        templAbstract: child.templAbstract,
        ordernum: child.ordernum,
        certTemplId: template.id
      }
      this.childDialogVisible = true
    },

    resetChildForm() {
      this.childForm = {
        id: null,
        name: '',
        fieldCode: '',
        fieldSummary: '',
        accountingSubjects: '',
        borrowing: 1,
        templAbstract: '',
        ordernum: 1,
        certTemplId: null
      }
      if (this.$refs.childForm) {
        this.$refs.childForm.clearValidate()
      }
    },

    async saveChild() {
      this.$refs.childForm.validate(async (valid) => {
        if (valid) {
          this.saving = true

          try {
            // 构建完整的模板数据
            const children = [...(this.currentTemplate.children || [])]


            if (this.childDialogMode === 'add') {
              // 添加新的子项
              children.push({ ...this.childForm })
            } else {
              // 更新现有子项
              const index = children.findIndex(child => child.id === this.childForm.id)
              if (index !== -1) {
                children[index] = { ...this.childForm }
              }
            }

            // 去除parent包装，直接使用扁平化数据结构
            const templateData = {
              id: this.currentTemplate.id,
              name: this.currentTemplate.name,
              code: this.currentTemplate.code,
              appCode: this.currentTemplate.appCode,
              regularExpressions: this.currentTemplate.regularExpressions || '',
              children: children
            }

            await updateCertTemplVo(templateData)

            // 新增会计科目时不自动绑定到帐套，保持独立维护
            // 用户可以后续通过界面手动建立绑定关系

            this.$message.success(this.childDialogMode === 'add' ? '添加成功' : '保存成功')
            this.childDialogVisible = false
            this.loadCertTemplList()
            this.loadCertTemplTempList() // 重新加载绑定关系
          } catch (err) {
            this.$message.error(err.message || '操作失败')
          } finally {
            this.saving = false
          }
        }
      })
    },

    // 凭证科目管理相关方法
    loadTokenCompanyList() {
      return getTokenCompanyList().then(res => {
        this.tokenCompanyList = res.list || res || []
      })
    },

    loadCertTemplTempList() {
      return getCertTemplTempList().then(res => {
        this.certTemplTempList = res.list || res || []
      })
    },

    // 管理模板帐套
    manageTemplateAccounts(template) {
      this.currentManageTemplate = template
      this.accountManageDialogVisible = true
    },

    // 显示批量选择帐套对话框
    showBatchSelectDialog() {
      if (!this.currentManageTemplate) {
        this.$message.warning('请先选择一个模板')
        return
      }
      this.prepareBatchSelectData()
      this.batchSelectDialogVisible = true
    },

    // 准备批量选择数据
    prepareBatchSelectData() {
      if (!this.currentManageTemplate) return

      // 获取当前模板已选择的帐套
      const selectedAccountIds = this.certTemplTempList
        .filter(item => item.templ_id === this.currentManageTemplate.id)
        .map(item => `${item.company_id}-${item.acc_model}`)

      // 准备税务核算帐套数据
      this.taxAccounts = this.tokenCompanyList
        .filter(account => account.accModel === 1)
        .map(account => ({
          ...account,
          selected: selectedAccountIds.includes(`${account.companyId}-1`)
        }))

      // 准备股东核算帐套数据
      this.shareholderAccounts = this.tokenCompanyList
        .filter(account => account.accModel === 2)
        .map(account => ({
          ...account,
          selected: selectedAccountIds.includes(`${account.companyId}-2`)
        }))
    },

    // 全选帐套
    selectAllAccounts() {
      this.taxAccounts.forEach(account => {
        account.selected = true
      })
      this.shareholderAccounts.forEach(account => {
        account.selected = true
      })
    },

    // 清空选择
    clearAllAccounts() {
      this.taxAccounts.forEach(account => {
        account.selected = false
      })
      this.shareholderAccounts.forEach(account => {
        account.selected = false
      })
    },

    // 保存批量选择
    async saveBatchSelection() {
      if (!this.currentManageTemplate) {
        this.$message.warning('请先选择模板')
        return
      }

      this.saving = true
      try {
        // 获取当前选中的帐套
        const selectedAccounts = [
          ...this.taxAccounts.filter(account => account.selected),
          ...this.shareholderAccounts.filter(account => account.selected)
        ]

        // 获取当前模板的所有绑定关系
        const currentBindings = this.certTemplTempList.filter(item =>
          item.templ_id === this.currentManageTemplate.id
        )

        // 需要删除的绑定关系
        const toRemove = currentBindings.filter(binding => {
          const accountKey = `${binding.company_id}-${binding.acc_model}`
          return !selectedAccounts.some(account =>
            `${account.companyId}-${account.accModel}` === accountKey
          )
        })

        // 需要添加的绑定关系
        const toAdd = selectedAccounts.filter(account => {
          const accountKey = `${account.companyId}-${account.accModel}`
          return !currentBindings.some(binding =>
            `${binding.company_id}-${binding.acc_model}` === accountKey
          )
        })

        // 执行删除操作
        if (toRemove.length > 0) {
          await removeCertTemplTemp({
            templ_id: this.currentManageTemplate.id,
            items: toRemove.map(item => ({
              company_id: item.company_id,
              acc_model: item.acc_model
            }))
          })
        }

        // 执行添加操作
        if (toAdd.length > 0) {
          // 只为帐套创建模板级别的绑定关系，不自动绑定到具体会计科目
          const addData = []
          toAdd.forEach(account => {
            // 创建模板级别的绑定，relation_id 设为 0 表示未绑定具体科目
            addData.push({
              company_id: account.companyId,
              acc_model: account.accModel,
              templ_id: this.currentManageTemplate.id,
              relation_id: 0 // 0 表示模板级别绑定，未绑定具体科目
            })
          })
          await saveCertTemplTemp(addData)
        }

        this.$message.success('保存成功')
        this.batchSelectDialogVisible = false
        await this.loadCertTemplTempList()
      } catch (error) {
        this.$message.error('保存失败')
        console.error(error)
      } finally {
        this.saving = false
      }
    },

    // 获取模板的帐套绑定信息
    getTemplateAccountInfo(template) {
      const bindings = this.certTemplTempList.filter(item => item.templ_id === template.id)
      const taxCount = bindings.filter(item => item.acc_model === 1).length
      const shareholderCount = bindings.filter(item => item.acc_model === 2).length

      if (taxCount === 0 && shareholderCount === 0) {
        return '未配置帐套'
      }

      const parts = []
      if (taxCount > 0) parts.push(`税务核算(${taxCount})`)
      if (shareholderCount > 0) parts.push(`股东核算(${shareholderCount})`)

      return parts.join(' | ')
    },

    // 移除模板的某个公司帐套
    async removeTemplateAccount(templateId, companyId, accModel) {
      try {
        await this.$confirm('确定要移除这个帐套绑定吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        await removeCertTemplTemp({
          templId: templateId,
          companyId: companyId,
          accModel: accModel
        })

        this.$message.success('移除成功')
        await this.loadCertTemplTempList()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('移除失败')
          console.error(error)
        }
      }
    },

    // 获取当前模板的税务核算帐套
    getTaxAccounts() {
      if (!this.currentManageTemplate) return []

      const bindings = this.certTemplTempList.filter(item =>
        item.templId === this.currentManageTemplate.id && item.accModel === 1
      )

      return bindings.map(binding => {
        const account = this.tokenCompanyList.find(tc =>
          tc.companyId === binding.companyId && tc.accModel === 1
        )
        return account || null
      }).filter(Boolean)
    },

    // 获取当前模板的股东核算帐套
    getShareholderAccounts() {
      if (!this.currentManageTemplate) return []

      const bindings = this.certTemplTempList.filter(item =>
        item.templId === this.currentManageTemplate.id && item.accModel === 2
      )

      return bindings.map(binding => {
        const account = this.tokenCompanyList.find(tc =>
          tc.companyId === binding.companyId && tc.accModel === 2
        )
        return account || null
      }).filter(Boolean)
    },

    // 获取税务核算帐套数量
    getTaxAccountsCount() {
      return this.getTaxAccounts().length
    },

    // 获取股东核算帐套数量
    getShareholderAccountsCount() {
      return this.getShareholderAccounts().length
    },

    // 获取会计科目关联的帐套信息
    getChildAccountSets(templateId, childId) {
      // 检查数据是否已加载
      if (!this.certTemplTempList || !this.tokenCompanyList ||
          this.certTemplTempList.length === 0 || this.tokenCompanyList.length === 0) {
        return []
      }

      // 获取与该会计科目相关的绑定关系
      const bindings = this.certTemplTempList.filter(item =>
        item.templId === templateId && item.relationId === childId
      )

      // 根据绑定关系获取对应的公司帐套信息
      const accountSets = bindings.map(binding => {
        const account = this.tokenCompanyList.find(tc =>
          tc.companyId === binding.companyId && tc.accModel === binding.accModel
        )

        if (account) {
          return {
            companyId: account.companyId,
            companyName: account.companyName,
            accModel: account.accModel,
            appName: account.appName,
            appCode: account.appCode
          }
        }
        return null
      }).filter(Boolean) // 过滤掉null值

      return accountSets
    },

    // 管理会计科目的帐套绑定
    manageChildAccountSets(child, template) {
      this.currentChildSubject = child
      this.currentChildTemplate = template
      this.childAccountBindingDialogVisible = true
    },

    // 获取可用的税务核算帐套（模板级别已绑定的）
    getAvailableTaxAccounts() {
      if (!this.currentChildTemplate) return []

      // 获取模板级别的税务核算帐套
      const templateTaxAccounts = this.certTemplTempList
        .filter(item =>
          item.templId === this.currentChildTemplate.id &&
          item.accModel === 1 &&
          item.relation_id === 0
        )
        .map(item => item.company_id)

      // 获取当前科目已绑定的税务核算帐套
      const boundTaxAccounts = this.certTemplTempList
        .filter(item =>
          item.templ_id === this.currentChildTemplate.id &&
          item.acc_model === 1 &&
          item.relation_id === this.currentChildSubject.id
        )
        .map(item => item.company_id)

      // 返回可用的帐套列表，标记绑定状态
      return this.tokenCompanyList
        .filter(account =>
          account.accModel === 1 &&
          templateTaxAccounts.includes(account.companyId)
        )
        .map(account => ({
          ...account,
          bound: boundTaxAccounts.includes(account.companyId)
        }))
    },

    // 获取可用的股东核算帐套（模板级别已绑定的）
    getAvailableShareholderAccounts() {
      if (!this.currentChildTemplate) return []

      // 获取模板级别的股东核算帐套
      const templateShareholderAccounts = this.certTemplTempList
        .filter(item =>
          item.templ_id === this.currentChildTemplate.id &&
          item.acc_model === 2 &&
          item.relation_id === 0
        )
        .map(item => item.company_id)

      // 获取当前科目已绑定的股东核算帐套
      const boundShareholderAccounts = this.certTemplTempList
        .filter(item =>
          item.templ_id === this.currentChildTemplate.id &&
          item.acc_model === 2 &&
          item.relation_id === this.currentChildSubject.id
        )
        .map(item => item.company_id)

      // 返回可用的帐套列表，标记绑定状态
      return this.tokenCompanyList
        .filter(account =>
          account.accModel === 2 &&
          templateShareholderAccounts.includes(account.companyId)
        )
        .map(account => ({
          ...account,
          bound: boundShareholderAccounts.includes(account.companyId)
        }))
    },

    // 切换会计科目与帐套的绑定状态
    async toggleChildAccountBinding(account, accModel) {
      if (!this.currentChildSubject || !this.currentChildTemplate) {
        this.$message.error('请先选择会计科目')
        return
      }

      try {
        if (account.bound) {
          // 创建绑定
          await saveCertTemplTemp([{
            company_id: account.companyId,
            acc_model: accModel,
            templ_id: this.currentChildTemplate.id,
            relation_id: this.currentChildSubject.id
          }])
          this.$message.success('绑定成功')
        } else {
          // 移除绑定
          await removeCertTemplTemp({
            templ_id: this.currentChildTemplate.id,
            company_id: account.companyId,
            acc_model: accModel,
            relation_id: this.currentChildSubject.id
          })
          this.$message.success('解绑成功')
        }

        // 重新加载数据
        await this.loadCertTemplTempList()
      } catch (error) {
        this.$message.error('操作失败')
        console.error(error)
        // 恢复开关状态
        account.bound = !account.bound
      }
    }
  }
}
</script>
<style scoped>
.cert-template-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 公司帐套管理区域样式 */
.company-account-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.section-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.hierarchy-switches {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

.switch-level {
  display: flex;
  align-items: center;
  gap: 8px;
}

.switch-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
  white-space: nowrap;
}

.current-selection {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 16px;
  margin-top: 16px;
}

.selection-info {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.info-item {
  font-size: 14px;
  color: #606266;
}

.info-item strong {
  color: #303133;
  margin-right: 4px;
}

/* 公司帐套管理对话框样式 */
.company-account-management {
  padding: 0;
}

.account-list-section {
  margin-bottom: 20px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.list-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

/* 公司帐套列表样式 */
.company-account-list {
  margin-top: 20px;
}

.list-title {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.account-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.account-card {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.account-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.account-card.selected {
  border-color: #409eff;
  background-color: #f0f9ff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.account-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.company-name {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.account-card-body {
  margin-bottom: 12px;
}

.account-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.info-row {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.info-row .label {
  color: #909399;
  width: 80px;
  flex-shrink: 0;
}

.info-row .value {
  color: #606266;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.account-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.create-time {
  font-size: 12px;
  color: #c0c4cc;
}

.card-actions {
  display: flex;
  gap: 8px;
}

.search-bar {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-start;
}

.template-cards-container {
  margin-bottom: 20px;
}

.empty-state {
  text-align: center;
  padding: 60px 0;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(600px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.template-card {
  width: 100%;
}

.parent-card {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.parent-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.template-info {
  flex: 1;
}

.template-name {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
}

.card-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
  margin-left: 16px;
}

.template-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 8px;
}

.meta-time {
  font-size: 12px;
  color: #909399;
  background-color: #f5f7fa;
  padding: 2px 8px;
  border-radius: 12px;
}

.meta-regex {
  font-size: 12px;
  color: #67c23a;
  background-color: #f0f9ff;
  padding: 2px 8px;
  border-radius: 12px;
  border: 1px solid #b3e19d;
  cursor: help;
}

.meta-accounts {
  background-color: #f6ffed;
  color: #52c41a;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  border: 1px solid #b7eb8f;
  cursor: help;
}

.children-section {
  margin-top: 16px;
  border-top: 1px solid #ebeef5;
  padding-top: 16px;
}

.children-header {
  padding: 8px 0;
}

.children-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  display: flex;
  align-items: center;
}

.children-title i {
  margin-right: 8px;
  transition: transform 0.3s ease;
}

.children-list {
  margin-top: 12px;
}

.child-item {
  background-color: #fafbfc;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 12px;
  transition: all 0.3s ease;
}

.child-item:hover {
  border-color: #c0c4cc;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.child-content {
  padding: 16px;
  display: grid;
  grid-template-columns: 1fr 2fr auto;
  gap: 16px;
  align-items: start;
}

.child-main {
  min-width: 0;
}

.child-name {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  line-height: 1.4;
}

.child-summary {
  margin: 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.4;
  word-break: break-all;
}

.child-details {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.detail-row {
  display: flex;
  font-size: 14px;
  line-height: 1.4;
}

.detail-label {
  color: #909399;
  width: 50px;
  flex-shrink: 0;
}

.detail-value {
  color: #606266;
  flex: 1;
  word-break: break-all;
}

/* 帐套信息样式 */
.account-sets-row {
  flex-direction: column;
  align-items: flex-start;
}

.account-sets-row .detail-label {
  margin-bottom: 6px;
}

.account-sets-info {
  width: 100%;
}

.no-accounts {
  padding: 4px 0;
}

.no-accounts-text {
  color: #c0c4cc;
  font-size: 12px;
  font-style: italic;
}

.account-sets-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.account-set-item {
  margin-bottom: 4px;
}

.account-tag {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 3px;
}

.child-actions {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex-shrink: 0;
}

.expand-toggle-btn {
  text-align: center;
  padding: 12px 0;
  border-top: 1px dashed #e4e7ed;
  margin-top: 8px;
}

.toggle-btn {
  color: #409eff;
  font-size: 14px;
}

.toggle-btn:hover {
  color: #66b1ff;
}

.toggle-btn i {
  margin-right: 4px;
}

.add-child-btn {
  text-align: center;
  padding: 16px;
  border-top: 1px dashed #d3d4d6;
  margin-top: 8px;
}

.add-template-btn-top {
  text-align: left;
  margin-bottom: 20px;
}

.add-template-btn {
  text-align: center;
  padding: 20px 0;
}

/* 动画效果 */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter,
.slide-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cards-grid {
    grid-template-columns: 1fr;
  }

  .child-content {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .child-actions {
    flex-direction: row;
    justify-content: flex-end;
  }

  .card-header {
    flex-direction: column;
    align-items: stretch;
  }

  .card-actions {
    margin-left: 0;
    margin-top: 12px;
    justify-content: flex-end;
  }
}

@media (max-width: 480px) {
  .cert-template-container {
    padding: 12px;
  }

  .search-bar {
    margin-bottom: 16px;
  }

  .search-bar .el-input {
    width: 100% !important;
  }
}

/* 帐套管理对话框样式 */
.account-manage-content {
  max-height: 600px;
  overflow-y: auto;
}

.manage-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
}

.template-info .info-label {
  font-weight: 500;
  color: #606266;
}

.template-info .info-value {
  font-weight: 600;
  color: #303133;
}

.account-categories {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.category-section {
  background: #fafafa;
  border-radius: 8px;
  padding: 16px;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.category-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.category-title i {
  font-size: 18px;
  color: #409eff;
}

.category-count {
  font-size: 14px;
  color: #909399;
  background: #f0f0f0;
  padding: 2px 8px;
  border-radius: 12px;
}

.accounts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 12px;
}

.empty-category {
  grid-column: 1 / -1;
  padding: 40px;
  text-align: center;
}

/* 批量选择对话框样式 */
.batch-select-content {
  max-height: 600px;
  overflow-y: auto;
}

.select-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
}

.batch-actions {
  display: flex;
  gap: 8px;
}

.accounts-selection {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.selection-section h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.account-checkbox {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px;
  background: #fafafa;
  transition: all 0.3s ease;
}

.account-checkbox:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.account-checkbox.is-checked {
  border-color: #409eff;
  background: #f0f9ff;
}

.account-checkbox .account-info {
  margin-left: 8px;
}

.account-checkbox .account-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.account-checkbox .account-detail {
  font-size: 12px;
  color: #909399;
}

/* 会计科目帐套绑定对话框样式 */
.child-account-binding-content {
  max-height: 500px;
  overflow-y: auto;
}

.binding-header {
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
}

.subject-info .info-label {
  font-weight: 500;
  color: #606266;
}

.subject-info .info-value {
  font-weight: 600;
  color: #303133;
}

.binding-categories {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.binding-section {
  background: #fafafa;
  border-radius: 8px;
  padding: 16px;
}

.binding-section-header {
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.binding-section .section-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.binding-section .section-title i {
  font-size: 18px;
  color: #409eff;
}

.account-binding-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.account-binding-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px;
  transition: all 0.3s ease;
}

.account-binding-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.account-binding-item .account-info {
  flex: 1;
}

.account-binding-item .company-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.account-binding-item .account-detail {
  font-size: 12px;
  color: #909399;
}

.binding-action {
  margin-left: 16px;
}

.empty-accounts {
  text-align: center;
  padding: 20px;
  color: #909399;
  font-style: italic;
}
</style>
